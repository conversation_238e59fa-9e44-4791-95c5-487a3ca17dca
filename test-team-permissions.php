<?php
/**
 * Test file for team chapter editing permissions
 * This file can be used to test the team member editing functionality
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test function to verify team member permissions
 */
function test_team_chapter_permissions() {
    // This function can be called to test the permissions system
    
    echo "<h2>اختبار صلاحيات تعديل الفصول للفرق</h2>";
    
    // Get current user
    $current_user_id = get_current_user_id();
    if (!$current_user_id) {
        echo "<p style='color: red;'>يجب تسجيل الدخول أولاً</p>";
        return;
    }
    
    echo "<p>المستخدم الحالي: " . get_userdata($current_user_id)->display_name . "</p>";
    
    // Get user's teams
    global $wpdb;
    $user_teams = $wpdb->get_results($wpdb->prepare(
        "SELECT t.* FROM {$wpdb->prefix}teams t
         INNER JOIN {$wpdb->prefix}team_members tm ON t.id = tm.team_id
         WHERE tm.user_id = %d",
        $current_user_id
    ));
    
    if (empty($user_teams)) {
        echo "<p style='color: orange;'>أنت لست عضواً في أي فريق حالياً</p>";
        return;
    }
    
    echo "<h3>الفرق التي أنت عضو فيها:</h3>";
    echo "<ul>";
    foreach ($user_teams as $team) {
        echo "<li>" . esc_html($team->name) . " (ID: " . $team->id . ")</li>";
    }
    echo "</ul>";
    
    // Get chapters published by teams
    $team_chapters = $wpdb->get_results(
        "SELECT p.ID, p.post_title, pm1.meta_value as team_id, pm2.meta_value as publish_as_team
         FROM {$wpdb->posts} p
         LEFT JOIN {$wpdb->postmeta} pm1 ON p.ID = pm1.post_id AND pm1.meta_key = '_team_id'
         LEFT JOIN {$wpdb->postmeta} pm2 ON p.ID = pm2.post_id AND pm2.meta_key = '_publish_as_team'
         WHERE p.post_type = 'chapter' AND pm2.meta_value = '1'
         ORDER BY p.post_date DESC
         LIMIT 10"
    );
    
    if (!empty($team_chapters)) {
        echo "<h3>الفصول المنشورة كفريق:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>عنوان الفصل</th><th>معرف الفريق</th><th>يمكنك التعديل؟</th></tr>";
        
        foreach ($team_chapters as $chapter) {
            $can_edit = false;
            if ($chapter->team_id) {
                // Check if user is member of this team
                $is_member = $wpdb->get_var($wpdb->prepare(
                    "SELECT COUNT(*) FROM {$wpdb->prefix}team_members 
                     WHERE user_id = %d AND team_id = %d",
                    $current_user_id,
                    $chapter->team_id
                ));
                $can_edit = $is_member > 0;
            }
            
            $edit_status = $can_edit ? "<span style='color: green;'>نعم</span>" : "<span style='color: red;'>لا</span>";
            echo "<tr>";
            echo "<td>" . esc_html($chapter->post_title) . "</td>";
            echo "<td>" . esc_html($chapter->team_id) . "</td>";
            echo "<td>" . $edit_status . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>لا توجد فصول منشورة كفريق حالياً</p>";
    }
}

// Add admin page for testing (only for administrators)
if (current_user_can('manage_options')) {
    add_action('admin_menu', function() {
        add_submenu_page(
            'team-system',
            'اختبار صلاحيات الفرق',
            'اختبار الصلاحيات',
            'manage_options',
            'test-team-permissions',
            'test_team_chapter_permissions'
        );
    });
}
