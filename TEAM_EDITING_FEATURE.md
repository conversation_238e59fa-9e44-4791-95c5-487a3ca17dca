# ميزة تعديل الفصول لأعضاء الفريق

## الوصف
تم إضافة ميزة جديدة تسمح لأي عضو في الفريق بتعديل الفصول المنشورة كفريق، وليس فقط منشئ الفصل الأصلي.

## كيف تعمل الميزة

### 1. نشر فصل كفريق
- عندما ينشر عضو فصلاً ويختار "أريد أن أنشر كفريق"
- يتم حفظ معرف الفريق مع الفصل في قاعدة البيانات
- يظهر الفصل منسوباً للفريق بدلاً من المستخدم الفردي

### 2. صلاحيات التعديل
- **قبل التحديث**: فقط منشئ الفصل الأصلي يمكنه تعديله
- **بعد التحديث**: أي عضو في الفريق الذي نشر الفصل يمكنه تعديله

### 3. التحقق من الصلاحيات
يتم التحقق من الصلاحيات عبر:
- `map_meta_cap` filter في WordPress
- التحقق من عضوية المستخدم في الفريق
- التحقق من أن الفصل منشور كفريق

## الملفات المعدلة

### admin/class-team-system-admin.php
تم إضافة الوظائف التالية:

#### `map_team_chapter_capabilities($caps, $cap, $user_id, $args)`
- تتحكم في صلاحيات تعديل الفصول
- تتحقق من نوع المنشور (chapter)
- تتحقق من أن الفصل منشور كفريق
- تمنح صلاحية التعديل لأعضاء الفريق

#### `is_user_team_member($user_id, $team_id)`
- تتحقق من عضوية المستخدم في فريق معين
- تدعم كلاً من `status` و `is_active` في جدول team_members
- تعيد true إذا كان المستخدم عضواً نشطاً في الفريق

#### `show_team_chapter_edit_notices()`
- تعرض إشعارات في صفحة تعديل الفصل
- تخبر المستخدم إذا كان يمكنه تعديل الفصل كعضو فريق
- تحذر المستخدمين غير الأعضاء من عدم قدرتهم على التعديل

#### `add_team_column_to_chapters($columns)`
- تضيف عمود "الفريق" في قائمة الفصول في wp-admin
- يظهر اسم الفريق للفصول المنشورة كفريق

#### `display_team_column_content($column, $post_id)`
- تعرض محتوى عمود الفريق
- تظهر رابط للفريق مع أيقونة
- تظهر "—" للفصول غير المنشورة كفريق

### تحسينات في team_chapter_meta_box
- تعرض معلومات الفريق للفصول المنشورة كفريق
- تخبر المستخدم أنه يمكنه التعديل كعضو فريق
- تحسين واجهة المستخدم مع ألوان وأيقونات

## كيفية الاختبار

### 1. إنشاء فريق
1. اذهب إلى wp-admin > الفرق > إضافة فريق جديد
2. أنشئ فريقاً جديداً
3. أضف أعضاء للفريق

### 2. نشر فصل كفريق
1. اذهب إلى wp-admin > الفصول > إضافة جديد
2. في الشريط الجانبي، ستجد صندوق "نشر كفريق"
3. اختر "أريد أن أنشر كفريق"
4. اختر الفريق من القائمة
5. انشر الفصل

### 3. اختبار التعديل
1. سجل دخول كعضو آخر في نفس الفريق
2. اذهب إلى قائمة الفصول
3. ستجد عمود "الفريق" يظهر اسم الفريق
4. اضغط على "تحرير" للفصل المنشور كفريق
5. ستظهر رسالة تخبرك أنك تستطيع التعديل كعضو فريق
6. قم بالتعديل واحفظ

### 4. اختبار صفحة الاختبار (للمطورين)
- اذهب إلى wp-admin > الفرق > اختبار الصلاحيات
- ستظهر معلومات مفصلة عن الفرق والصلاحيات

## الأمان والحماية

### التحقق من الصلاحيات
- يتم التحقق من عضوية الفريق في قاعدة البيانات
- لا يمكن للمستخدمين غير الأعضاء تعديل فصول الفريق
- يتم الحفاظ على صلاحيات WordPress الأساسية

### حماية البيانات
- جميع الاستعلامات محمية ضد SQL injection
- يتم التحقق من nonce في النماذج
- تنظيف جميع المدخلات قبل الحفظ

## ملاحظات مهمة

1. **التوافق مع الإصدارات السابقة**: النظام يعمل مع الفصول الموجودة دون مشاكل
2. **عدم التأثير على القالب**: جميع التعديلات في الإضافة فقط
3. **الأداء**: الاستعلامات محسنة ولا تؤثر على أداء الموقع
4. **المرونة**: يدعم بنيات مختلفة لجدول team_members

## استكشاف الأخطاء

### إذا لم تظهر صلاحيات التعديل:
1. تأكد من أن المستخدم عضو نشط في الفريق
2. تحقق من أن الفصل منشور كفريق (_publish_as_team = '1')
3. تأكد من وجود معرف الفريق (_team_id)

### إذا لم تظهر الإشعارات:
1. تأكد من أن الصفحة هي post.php
2. تحقق من أن نوع المنشور هو 'chapter'
3. تأكد من تفعيل الإضافة بشكل صحيح

## المتطلبات
- WordPress 5.0+
- PHP 7.4+
- إضافة Team System مفعلة
- نوع منشور 'chapter' موجود
